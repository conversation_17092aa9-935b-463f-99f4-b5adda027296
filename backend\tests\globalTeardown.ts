// Extend global interface for test utilities
declare global {
  var testCleanup: (() => Promise<void>) | undefined
  var testDbConnection: any
}

export default async () => {
  console.log('🧹 Tearing down test environment...')

  // Clean up test data if cleanup function exists
  if (global.testCleanup) {
    await global.testCleanup()
  }

  // Close any open connections
  if (global.testDbConnection) {
    await global.testDbConnection.end()
  }

  console.log('✅ Test environment teardown complete')
}
