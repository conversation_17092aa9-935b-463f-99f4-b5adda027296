import dotenv from 'dotenv'
import { DatabaseService } from '../src/services/databaseService'

// Load test environment variables
dotenv.config({ path: '.env.test' })

// Global test configuration
jest.setTimeout(30000)

// Mock external services
jest.mock('../src/services/aiProviderService', () => ({
  AIProviderService: jest.fn().mockImplementation(() => ({
    parseResumeWithAI: jest.fn().mockResolvedValue({
      candidateName: 'Test Candidate',
      contact: { email: '<EMAIL>' },
      skills: [{ name: 'JavaScript', category: 'Programming', level: 'advanced' }],
      experience: [],
      education: [],
      certifications: [],
      languages: []
    }),
    analyzeSentimentWithAI: jest.fn().mockResolvedValue({
      sentiment: 'positive',
      confidence: 0.8,
      score: 0.6,
      emotions: [],
      keywords: []
    }),
    getProviderStatus: jest.fn().mockResolvedValue({
      resumeParsing: { enabled: true, configured: true },
      sentimentAnalysis: { enabled: true, configured: true }
    })
  }))
}))

// Mock Redis
jest.mock('redis', () => ({
  createClient: jest.fn(() => ({
    connect: jest.fn(),
    disconnect: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn()
  }))
}))

// Mock email service
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' })
  }))
}))

// Mock file upload
jest.mock('multer', () => {
  const multer = () => ({
    single: () => (req: any, _res: any, next: any) => {
      req.file = {
        originalname: 'test-resume.pdf',
        mimetype: 'application/pdf',
        size: 1024,
        buffer: Buffer.from('test file content')
      }
      next()
    },
    array: () => (req: any, _res: any, next: any) => {
      req.files = [{
        originalname: 'test-file.pdf',
        mimetype: 'application/pdf',
        size: 1024,
        buffer: Buffer.from('test file content')
      }]
      next()
    }
  })
  multer.memoryStorage = jest.fn()
  return multer
})

// Global test utilities
global.testUtils = {
  createMockUser: () => ({
    id: 'test-user-id',
    email: '<EMAIL>',
    role: 'employee',
    permissions: ['employee'],
    employeeId: 'test-employee-id',
    department: 'Engineering'
  }),
  
  createMockEmployee: () => ({
    id: 'test-employee-id',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    status: 'active',
    departmentId: 'test-dept-id',
    hireDate: new Date(),
    salary: 75000
  }),
  
  createMockRequest: (overrides = {}) => ({
    user: global.testUtils.createMockUser(),
    body: {},
    params: {},
    query: {},
    headers: {},
    ...overrides
  }),
  
  createMockResponse: () => {
    const res: any = {}
    res.status = jest.fn().mockReturnValue(res)
    res.json = jest.fn().mockReturnValue(res)
    res.send = jest.fn().mockReturnValue(res)
    res.cookie = jest.fn().mockReturnValue(res)
    res.clearCookie = jest.fn().mockReturnValue(res)
    return res
  },
  
  createMockNext: () => jest.fn(),
  
  sleep: (ms: number) => new Promise(resolve => setTimeout(resolve, ms))
}

// Database cleanup utility
global.cleanupDatabase = async () => {
  const db = new DatabaseService()
  try {
    // Clean up test data in reverse dependency order
    await db.query('DELETE FROM ai_training_feedback WHERE user_id LIKE \'test-%\'')
    await db.query('DELETE FROM ai_service_logs WHERE user_id LIKE \'test-%\'')
    await db.query('DELETE FROM parsed_resumes WHERE user_id LIKE \'test-%\'')
    await db.query('DELETE FROM sentiment_analyses WHERE user_id LIKE \'test-%\'')
    await db.query('DELETE FROM attrition_predictions WHERE employee_id LIKE \'test-%\'')
    await db.query('DELETE FROM performance_insights WHERE employee_id LIKE \'test-%\'')
    await db.query('DELETE FROM career_recommendations WHERE employee_id LIKE \'test-%\'')
    await db.query('DELETE FROM skills_gap_analyses WHERE employee_id LIKE \'test-%\'')
    await db.query('DELETE FROM nl_query_logs WHERE user_id LIKE \'test-%\'')
    await db.query('DELETE FROM audit_logs WHERE user_id LIKE \'test-%\'')
    await db.query('DELETE FROM employee_documents WHERE employee_id LIKE \'test-%\'')
    await db.query('DELETE FROM performance_reviews WHERE employee_id LIKE \'test-%\'')
    await db.query('DELETE FROM onboarding_tasks WHERE employee_id LIKE \'test-%\'')
    await db.query('DELETE FROM offboarding_tasks WHERE employee_id LIKE \'test-%\'')
    await db.query('DELETE FROM employees WHERE id LIKE \'test-%\'')
    await db.query('DELETE FROM users WHERE id LIKE \'test-%\'')
    await db.query('DELETE FROM departments WHERE id LIKE \'test-%\'')
  } catch (error) {
    console.warn('Database cleanup warning:', error)
  }
}

// Console override for cleaner test output
const originalConsole = console
global.console = {
  ...originalConsole,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
}

// Restore console for specific tests if needed
global.restoreConsole = () => {
  global.console = originalConsole
}

// Type declarations for global utilities
declare global {
  var testUtils: {
    createMockUser: () => any
    createMockEmployee: () => any
    createMockRequest: (overrides?: any) => any
    createMockResponse: () => any
    createMockNext: () => jest.Mock
    sleep: (ms: number) => Promise<void>
  }
  var cleanupDatabase: () => Promise<void>
  var restoreConsole: () => void
}
