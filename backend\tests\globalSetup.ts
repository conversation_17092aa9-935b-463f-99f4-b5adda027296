import dotenv from 'dotenv'
import { DatabaseService } from '../src/services/databaseService'

// Extend global interface for test utilities
declare global {
  var testCleanup: () => Promise<void>
}

export default async () => {
  // Load test environment
  dotenv.config({ path: '.env.test' })
  
  console.log('🚀 Setting up test environment...')
  
  // Set test environment variables
  process.env.NODE_ENV = 'test'
  process.env.DB_NAME = process.env.TEST_DB_NAME || 'peoplenest_test'
  process.env.JWT_SECRET = 'test-jwt-secret'
  process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters'
  process.env.AI_MOCK_MODE = 'true'
  
  // Initialize test database (skip if not available)
  try {
    const db = new DatabaseService()

    // Check if test database exists and is accessible
    await db.query('SELECT 1')
    console.log('✅ Test database connection established')

    // Run migrations if needed
    console.log('🔄 Checking database schema...')

    // Create test data cleanup function
    global.testCleanup = async () => {
      try {
        // Clean up any test data
        await db.query(`
          DELETE FROM ai_service_logs WHERE user_id LIKE 'test-%' OR user_id LIKE '%test%'
        `)
        await db.query(`
          DELETE FROM employees WHERE id LIKE 'test-%' OR email LIKE '%test%'
        `)
        await db.query(`
          DELETE FROM users WHERE id LIKE 'test-%' OR email LIKE '%test%'
        `)
        console.log('🧹 Test data cleaned up')
      } catch (error) {
        console.warn('⚠️ Test cleanup warning:', error)
      }
    }

  } catch (error) {
    console.warn('⚠️ Database not available for testing, using mocks only:', error instanceof Error ? error.message : String(error))

    // Create mock cleanup function
    global.testCleanup = async () => {
      console.log('🧹 Mock test cleanup completed')
    }
  }
  
  console.log('✅ Test environment setup complete')
}
